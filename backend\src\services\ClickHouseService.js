/**
 * ClickHouse服务 - Node.js版本
 * 提供历史数据查询和实时数据同步功能
 */

const axios = require('axios');

class ClickHouseService {
    constructor() {
        this.tableName = process.env.CLICKHOUSE_TABLE || 'force_orders';
        this.database = process.env.CLICKHOUSE_DATABASE || 'mydatabase';

        // ClickHouse连接配置
        const host = process.env.CLICKHOUSE_HOST || 'localhost';
        const port = parseInt(process.env.CLICKHOUSE_PORT) || 8123;
        const secure = process.env.CLICKHOUSE_SECURE === 'true';
        const user = process.env.CLICKHOUSE_USER || 'default';
        const password = process.env.CLICKHOUSE_PASSWORD || '';

        // 调试信息
        console.log('🔍 ClickHouse环境变量调试:');
        console.log(`   CLICKHOUSE_HOST: ${process.env.CLICKHOUSE_HOST}`);
        console.log(`   CLICKHOUSE_PORT: ${process.env.CLICKHOUSE_PORT}`);
        console.log(`   CLICKHOUSE_USER: ${process.env.CLICKHOUSE_USER}`);
        console.log(`   CLICKHOUSE_PASSWORD: ${process.env.CLICKHOUSE_PASSWORD ? '***设置***' : '未设置'}`);
        console.log(`   CLICKHOUSE_DATABASE: ${process.env.CLICKHOUSE_DATABASE}`);
        console.log(`   CLICKHOUSE_TABLE: ${process.env.CLICKHOUSE_TABLE}`);

        // HTTP配置
        this.baseURL = `${secure ? 'https' : 'http'}://${host}:${port}`;
        this.auth = Buffer.from(`${user}:${password}`).toString('base64');
        this.timeout = parseInt(process.env.CLICKHOUSE_CONNECT_TIMEOUT) * 1000 || 30000;

        console.log('🔧 HTTP ClickHouse配置:');
        console.log(`   Base URL: ${this.baseURL}`);
        console.log(`   数据库: ${this.database}`);
        console.log(`   表名: ${this.tableName}`);
        console.log(`   认证: ${user ? '已启用' : '未启用'}`);
        console.log(`   超时: ${this.timeout}ms`);
    }
    
    async connect() {
        try {
            // 测试连接
            const result = await this.query('SELECT 1 as test');
            console.log('✅ ClickHouse HTTP连接成功:', result);

            // 验证表是否存在
            await this.verifyTable();

        } catch (error) {
            console.error('❌ ClickHouse连接失败:', error);
            throw error;
        }
    }
    
    async disconnect() {
        // HTTP连接不需要显式断开连接
        console.log('🔌 ClickHouse HTTP连接已关闭');
    }

    /**
     * 执行HTTP查询
     */
    async query(sql, params = {}) {
        try {
            const url = new URL(this.baseURL);
            url.searchParams.set('query', sql);
            url.searchParams.set('database', this.database);
            url.searchParams.set('default_format', 'JSON');

            // 添加其他参数
            Object.keys(params).forEach(key => {
                url.searchParams.set(key, params[key]);
            });

            const response = await axios.get(url.toString(), {
                headers: {
                    'Authorization': `Basic ${this.auth}`,
                    'Content-Type': 'text/plain',
                    'Accept': 'application/json'
                },
                timeout: this.timeout
            });

            return response.data;

        } catch (error) {
            console.error('❌ ClickHouse查询失败:', error.message);
            if (error.response) {
                console.error('   状态码:', error.response.status);
                console.error('   响应:', error.response.data);
            }
            throw error;
        }
    }
    
    async verifyTable() {
        try {
            const query = `
                SELECT count() as count
                FROM system.tables
                WHERE database = '${this.database}'
                AND name = '${this.tableName}'
            `;

            const result = await this.query(query);

            if (result && result.data && result.data.length > 0 && result.data[0].count > 0) {
                console.log(`✅ 表 ${this.tableName} 存在`);

                // 获取表结构信息
                const tableInfo = await this.getTableInfo();
                console.log(`📊 表记录数: ${tableInfo.total_rows}`);

            } else {
                console.warn(`⚠️ 表 ${this.tableName} 不存在`);
            }

        } catch (error) {
            console.error('❌ 验证表失败:', error);
            // 不抛出错误，允许系统继续运行
        }
    }
    
    async getTableInfo() {
        try {
            const query = `
                SELECT
                    count() as total_rows,
                    min(event_time) as earliest_time,
                    max(event_time) as latest_time,
                    uniq(symbol) as unique_symbols
                FROM ${this.tableName}
            `;

            const result = await this.query(query);

            if (result && result.data && result.data.length > 0) {
                return result.data[0];
            }

            return {
                total_rows: 0,
                earliest_time: null,
                latest_time: null,
                unique_symbols: 0
            };

        } catch (error) {
            console.error('❌ 获取表信息失败:', error);
            return {
                total_rows: 0,
                earliest_time: null,
                latest_time: null,
                unique_symbols: 0
            };
        }
    }
    
    async getRecentLiquidations(limit = 100, symbols = null) {
        try {
            let whereClause = '';
            if (symbols && symbols.length > 0) {
                const symbolList = symbols.map(s => `'${s}'`).join(',');
                whereClause = `WHERE symbol IN (${symbolList})`;
            }
            
            const query = `
                SELECT 
                    event_time,
                    symbol,
                    side,
                    order_type,
                    quantity,
                    price,
                    avg_price,
                    status,
                    trade_time
                FROM ${this.tableName}
                ${whereClause}
                ORDER BY event_time DESC
                LIMIT ${limit}
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询最近 ${result.length} 条强制平仓记录`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询最近强制平仓记录失败:', error);
            return [];
        }
    }
    
    async getLiquidationsByTimeRange(startTime, endTime, symbols = null) {
        try {
            let whereClause = `WHERE event_time >= '${startTime}' AND event_time <= '${endTime}'`;
            
            if (symbols && symbols.length > 0) {
                const symbolList = symbols.map(s => `'${s}'`).join(',');
                whereClause += ` AND symbol IN (${symbolList})`;
            }
            
            const query = `
                SELECT 
                    event_time,
                    symbol,
                    side,
                    order_type,
                    quantity,
                    price,
                    avg_price,
                    status,
                    trade_time
                FROM ${this.tableName}
                ${whereClause}
                ORDER BY event_time ASC
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询时间范围 ${startTime} - ${endTime} 内 ${result.length} 条记录`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询时间范围数据失败:', error);
            return [];
        }
    }
    
    async getHourlyStatistics(hours = 24, symbols = null) {
        try {
            let whereClause = `WHERE event_time >= now() - INTERVAL ${hours} HOUR`;
            
            if (symbols && symbols.length > 0) {
                const symbolList = symbols.map(s => `'${s}'`).join(',');
                whereClause += ` AND symbol IN (${symbolList})`;
            }
            
            const query = `
                SELECT 
                    toStartOfHour(event_time) as hour,
                    symbol,
                    count() as liquidation_count,
                    sum(quantity * price) as total_volume_usd,
                    avg(quantity * price) as avg_volume_usd,
                    countIf(side = 'BUY') as buy_count,
                    countIf(side = 'SELL') as sell_count
                FROM ${this.tableName}
                ${whereClause}
                GROUP BY hour, symbol
                ORDER BY hour DESC, total_volume_usd DESC
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询最近 ${hours} 小时统计数据: ${result.length} 条记录`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询小时统计失败:', error);
            return [];
        }
    }
    
    async getTopSymbolsByVolume(limit = 10, hours = 24) {
        try {
            const query = `
                SELECT 
                    symbol,
                    count() as liquidation_count,
                    sum(quantity * price) as total_volume_usd,
                    avg(quantity * price) as avg_volume_usd,
                    countIf(side = 'BUY') as buy_count,
                    countIf(side = 'SELL') as sell_count,
                    max(event_time) as last_liquidation
                FROM ${this.tableName}
                WHERE event_time >= now() - INTERVAL ${hours} HOUR
                GROUP BY symbol
                ORDER BY total_volume_usd DESC
                LIMIT ${limit}
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询交易量前 ${limit} 的交易对`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询交易对排行失败:', error);
            return [];
        }
    }
    
    async getRiskAnalysis(symbols = null, hours = 24) {
        try {
            let whereClause = `WHERE event_time >= now() - INTERVAL ${hours} HOUR`;
            
            if (symbols && symbols.length > 0) {
                const symbolList = symbols.map(s => `'${s}'`).join(',');
                whereClause += ` AND symbol IN (${symbolList})`;
            }
            
            const query = `
                SELECT 
                    symbol,
                    count() as total_liquidations,
                    sum(quantity * price) as total_volume_usd,
                    avg(quantity * price) as avg_liquidation_size,
                    quantile(0.5)(quantity * price) as median_liquidation_size,
                    quantile(0.95)(quantity * price) as p95_liquidation_size,
                    max(quantity * price) as max_liquidation_size,
                    countIf(quantity * price > 10000) as large_liquidations,
                    countIf(quantity * price > 100000) as huge_liquidations,
                    -- 计算风险评分
                    CASE 
                        WHEN sum(quantity * price) > 1000000 THEN 'HIGH'
                        WHEN sum(quantity * price) > 100000 THEN 'MEDIUM'
                        ELSE 'LOW'
                    END as risk_level
                FROM ${this.tableName}
                ${whereClause}
                GROUP BY symbol
                ORDER BY total_volume_usd DESC
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询风险分析数据: ${result.length} 个交易对`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询风险分析失败:', error);
            return [];
        }
    }
    
    async getMarketSentimentData(hours = 24) {
        try {
            const query = `
                SELECT 
                    toStartOfHour(event_time) as hour,
                    count() as total_liquidations,
                    sum(quantity * price) as total_volume_usd,
                    countIf(side = 'BUY') as buy_liquidations,
                    countIf(side = 'SELL') as sell_liquidations,
                    -- 计算恐慌指数
                    (countIf(side = 'SELL') * 100.0 / count()) as sell_ratio,
                    -- 计算波动性指标
                    stddevPop(quantity * price) as volume_volatility,
                    -- 计算市场情绪
                    CASE 
                        WHEN countIf(side = 'SELL') > countIf(side = 'BUY') * 1.5 THEN 'FEAR'
                        WHEN countIf(side = 'BUY') > countIf(side = 'SELL') * 1.5 THEN 'GREED'
                        ELSE 'NEUTRAL'
                    END as sentiment
                FROM ${this.tableName}
                WHERE event_time >= now() - INTERVAL ${hours} HOUR
                GROUP BY hour
                ORDER BY hour DESC
            `;
            
            const result = await this.client.query(query).toPromise();
            
            console.log(`📊 查询市场情绪数据: ${result.length} 小时`);
            return result || [];
            
        } catch (error) {
            console.error('❌ 查询市场情绪失败:', error);
            return [];
        }
    }
    
    async streamHistoricalData(startTime, endTime, batchSize = 1000, symbols = null) {
        try {
            let whereClause = `WHERE event_time >= '${startTime}' AND event_time <= '${endTime}'`;
            
            if (symbols && symbols.length > 0) {
                const symbolList = symbols.map(s => `'${s}'`).join(',');
                whereClause += ` AND symbol IN (${symbolList})`;
            }
            
            let offset = 0;
            const results = [];
            
            while (true) {
                const query = `
                    SELECT 
                        event_time,
                        symbol,
                        side,
                        order_type,
                        quantity,
                        price,
                        avg_price,
                        status,
                        trade_time
                    FROM ${this.tableName}
                    ${whereClause}
                    ORDER BY event_time ASC
                    LIMIT ${batchSize} OFFSET ${offset}
                `;
                
                const batch = await this.client.query(query).toPromise();
                
                if (!batch || batch.length === 0) {
                    break;
                }
                
                results.push(...batch);
                offset += batchSize;
                
                console.log(`📊 流式读取批次: ${batch.length} 条记录 (总计: ${results.length})`);
                
                // 如果批次小于batchSize，说明已经读取完毕
                if (batch.length < batchSize) {
                    break;
                }
            }
            
            console.log(`📊 流式读取完成: 总计 ${results.length} 条记录`);
            return results;
            
        } catch (error) {
            console.error('❌ 流式读取历史数据失败:', error);
            return [];
        }
    }
    
    async getAvailableSymbols() {
        try {
            const query = `
                SELECT DISTINCT symbol
                FROM ${this.tableName}
                ORDER BY symbol
            `;
            
            const result = await this.client.query(query).toPromise();
            
            const symbols = result.map(row => row.symbol);
            console.log(`📊 获取可用交易对: ${symbols.length} 个`);
            
            return symbols;
            
        } catch (error) {
            console.error('❌ 获取可用交易对失败:', error);
            return [];
        }
    }
    
    async getDataTimeRange() {
        try {
            const query = `
                SELECT 
                    min(event_time) as start_time,
                    max(event_time) as end_time,
                    count() as total_records
                FROM ${this.tableName}
            `;
            
            const result = await this.client.query(query).toPromise();
            
            if (result && result.length > 0) {
                console.log(`📊 数据时间范围: ${result[0].start_time} - ${result[0].end_time}`);
                return result[0];
            }
            
            return null;
            
        } catch (error) {
            console.error('❌ 获取数据时间范围失败:', error);
            return null;
        }
    }
}

module.exports = ClickHouseService;
