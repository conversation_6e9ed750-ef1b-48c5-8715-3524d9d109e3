/**
 * 健康检查API路由
 * 提供系统状态和服务健康检查
 */

const express = require('express');
const router = express.Router();
const Redis = require('redis');
const { ClickHouse } = require('clickhouse');

// 健康检查缓存
let healthCache = {
    lastCheck: null,
    status: null,
    ttl: 30000 // 30秒缓存
};

// 基础健康检查
router.get('/', async (req, res) => {
    try {
        // 检查缓存
        const now = Date.now();
        if (healthCache.lastCheck && (now - healthCache.lastCheck) < healthCache.ttl) {
            return res.json(healthCache.status);
        }
        
        const status = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            services: {
                redis: await checkRedisHealth(),
                clickhouse: await checkClickHouseHealth(),
                websocket: checkWebSocketHealth()
            }
        };
        
        // 判断整体状态
        const serviceStatuses = Object.values(status.services);
        const hasUnhealthy = serviceStatuses.some(service => service.status !== 'healthy');
        
        if (hasUnhealthy) {
            status.status = 'degraded';
        }
        
        // 更新缓存
        healthCache.lastCheck = now;
        healthCache.status = status;
        
        // 根据状态设置HTTP状态码
        const httpStatus = status.status === 'healthy' ? 200 : 503;
        res.status(httpStatus).json(status);
        
    } catch (error) {
        console.error('健康检查失败:', error);
        res.status(500).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// 详细健康检查
router.get('/detailed', async (req, res) => {
    try {
        const detailedStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            system: {
                memory: process.memoryUsage(),
                cpu: process.cpuUsage(),
                platform: process.platform,
                arch: process.arch,
                node_version: process.version,
                pid: process.pid
            },
            environment: {
                node_env: process.env.NODE_ENV || 'development',
                port: process.env.WEBSOCKET_PORT || 3001,
                redis_host: process.env.REDIS_HOST || 'localhost',
                redis_port: process.env.REDIS_PORT || 6379,
                clickhouse_url: process.env.CLICKHOUSE_URL || 'http://localhost',
                clickhouse_port: process.env.CLICKHOUSE_PORT || 8123
            },
            services: {
                redis: await checkRedisHealthDetailed(),
                clickhouse: await checkClickHouseHealthDetailed(),
                websocket: checkWebSocketHealthDetailed()
            },
            features: {
                redis_stream: true,
                redis_pubsub: true,
                websocket_realtime: true,
                clickhouse_analytics: true,
                rate_limiting: true,
                cors_enabled: true,
                compression: true
            }
        };
        
        // 判断整体状态
        const serviceStatuses = Object.values(detailedStatus.services);
        const hasUnhealthy = serviceStatuses.some(service => service.status !== 'healthy');
        
        if (hasUnhealthy) {
            detailedStatus.status = 'degraded';
        }
        
        const httpStatus = detailedStatus.status === 'healthy' ? 200 : 503;
        res.status(httpStatus).json(detailedStatus);
        
    } catch (error) {
        console.error('详细健康检查失败:', error);
        res.status(500).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Redis健康检查
async function checkRedisHealth() {
    try {
        const client = Redis.createClient({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            db: parseInt(process.env.REDIS_DB) || 0,
            password: process.env.REDIS_PASSWORD,
            connectTimeout: 5000,
            lazyConnect: true
        });
        
        await client.connect();
        await client.ping();
        await client.quit();
        
        return {
            status: 'healthy',
            message: 'Redis连接正常',
            response_time: Date.now()
        };
        
    } catch (error) {
        return {
            status: 'unhealthy',
            message: 'Redis连接失败',
            error: error.message
        };
    }
}

// Redis详细健康检查
async function checkRedisHealthDetailed() {
    const startTime = Date.now();
    
    try {
        const client = Redis.createClient({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            db: parseInt(process.env.REDIS_DB) || 0,
            password: process.env.REDIS_PASSWORD,
            connectTimeout: 5000,
            lazyConnect: true
        });
        
        await client.connect();
        
        // 执行多个检查
        const pingResult = await client.ping();
        const info = await client.info();
        const memory = await client.info('memory');
        
        await client.quit();
        
        const responseTime = Date.now() - startTime;
        
        return {
            status: 'healthy',
            message: 'Redis连接正常',
            response_time: responseTime,
            ping_result: pingResult,
            details: {
                version: extractRedisVersion(info),
                memory_usage: extractMemoryUsage(memory),
                connected_clients: extractConnectedClients(info)
            }
        };
        
    } catch (error) {
        return {
            status: 'unhealthy',
            message: 'Redis连接失败',
            error: error.message,
            response_time: Date.now() - startTime
        };
    }
}

// ClickHouse健康检查
async function checkClickHouseHealth() {
    try {
        const host = process.env.CLICKHOUSE_HOST || 'localhost';
        const secure = process.env.CLICKHOUSE_SECURE === 'true';

        const client = new ClickHouse({
            url: `${secure ? 'https' : 'http'}://${host}`,
            port: parseInt(process.env.CLICKHOUSE_PORT) || 8123,
            basicAuth: process.env.CLICKHOUSE_USER ? {
                username: process.env.CLICKHOUSE_USER,
                password: process.env.CLICKHOUSE_PASSWORD
            } : null,
            config: {
                database: process.env.CLICKHOUSE_DATABASE || 'default'
            }
        });
        
        await client.query('SELECT 1').toPromise();
        
        return {
            status: 'healthy',
            message: 'ClickHouse连接正常'
        };
        
    } catch (error) {
        return {
            status: 'unhealthy',
            message: 'ClickHouse连接失败',
            error: error.message
        };
    }
}

// ClickHouse详细健康检查
async function checkClickHouseHealthDetailed() {
    const startTime = Date.now();
    
    try {
        const host = process.env.CLICKHOUSE_HOST || 'localhost';
        const secure = process.env.CLICKHOUSE_SECURE === 'true';

        const client = new ClickHouse({
            url: `${secure ? 'https' : 'http'}://${host}`,
            port: parseInt(process.env.CLICKHOUSE_PORT) || 8123,
            basicAuth: process.env.CLICKHOUSE_USER ? {
                username: process.env.CLICKHOUSE_USER,
                password: process.env.CLICKHOUSE_PASSWORD
            } : null,
            config: {
                database: process.env.CLICKHOUSE_DATABASE || 'default'
            }
        });
        
        // 执行多个检查
        const pingResult = await client.query('SELECT 1 as ping').toPromise();
        const versionResult = await client.query('SELECT version() as version').toPromise();
        
        // 检查表是否存在
        const tableName = process.env.CLICKHOUSE_TABLE || 'force_liquidation_orders';
        const tableCheck = await client.query(`
            SELECT count() as count 
            FROM system.tables 
            WHERE database = '${process.env.CLICKHOUSE_DATABASE || 'default'}' 
            AND name = '${tableName}'
        `).toPromise();
        
        const responseTime = Date.now() - startTime;
        
        return {
            status: 'healthy',
            message: 'ClickHouse连接正常',
            response_time: responseTime,
            details: {
                version: versionResult[0]?.version || 'unknown',
                table_exists: tableCheck[0]?.count > 0,
                table_name: tableName,
                database: process.env.CLICKHOUSE_DATABASE || 'default'
            }
        };
        
    } catch (error) {
        return {
            status: 'unhealthy',
            message: 'ClickHouse连接失败',
            error: error.message,
            response_time: Date.now() - startTime
        };
    }
}

// WebSocket健康检查
function checkWebSocketHealth() {
    // 简单检查WebSocket服务是否运行
    return {
        status: 'healthy',
        message: 'WebSocket服务运行中',
        port: process.env.WEBSOCKET_PORT || 3001
    };
}

// WebSocket详细健康检查
function checkWebSocketHealthDetailed() {
    return {
        status: 'healthy',
        message: 'WebSocket服务运行中',
        details: {
            port: process.env.WEBSOCKET_PORT || 3001,
            cors_origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
            transports: ['websocket', 'polling'],
            ping_timeout: 60000,
            ping_interval: 25000
        }
    };
}

// 辅助函数：提取Redis版本
function extractRedisVersion(info) {
    const match = info.match(/redis_version:([^\r\n]+)/);
    return match ? match[1] : 'unknown';
}

// 辅助函数：提取内存使用
function extractMemoryUsage(memory) {
    const match = memory.match(/used_memory_human:([^\r\n]+)/);
    return match ? match[1] : 'unknown';
}

// 辅助函数：提取连接客户端数
function extractConnectedClients(info) {
    const match = info.match(/connected_clients:([^\r\n]+)/);
    return match ? parseInt(match[1]) : 0;
}

// 就绪检查（用于Kubernetes等容器编排）
router.get('/ready', async (req, res) => {
    try {
        // 检查关键服务是否就绪
        const redisStatus = await checkRedisHealth();
        const clickhouseStatus = await checkClickHouseHealth();
        
        const isReady = redisStatus.status === 'healthy' && 
                       clickhouseStatus.status === 'healthy';
        
        if (isReady) {
            res.json({
                status: 'ready',
                timestamp: new Date().toISOString(),
                message: '服务已就绪'
            });
        } else {
            res.status(503).json({
                status: 'not_ready',
                timestamp: new Date().toISOString(),
                message: '服务未就绪',
                services: {
                    redis: redisStatus,
                    clickhouse: clickhouseStatus
                }
            });
        }
        
    } catch (error) {
        res.status(503).json({
            status: 'not_ready',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// 存活检查（用于Kubernetes等容器编排）
router.get('/live', (req, res) => {
    res.json({
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        message: '服务存活'
    });
});

module.exports = router;
