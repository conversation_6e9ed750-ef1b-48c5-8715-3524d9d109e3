{"name": "redis-liquidation-monitor-frontend", "version": "1.0.0", "description": "实时强制平仓监控大屏 - 前端应用", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^7.2.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/d3": "^7.4.3", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-grid-layout": "^1.3.5", "@types/styled-components": "^5.1.34", "antd": "^5.12.8", "d3": "^7.8.5", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-spring": "^9.7.3", "react-use-websocket": "^4.5.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "styled-components": "^6.1.6", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "sass": "^1.69.5"}, "proxy": "http://localhost:3001"}