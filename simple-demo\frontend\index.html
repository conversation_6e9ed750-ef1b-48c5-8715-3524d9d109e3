<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Redis Stream & Pub/Sub 增强演示</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        /* 控制面板 */
        .control-panel {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .control-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        /* 状态栏增强 */
        .status-bar {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            color: white;
        }
        
        .status-item {
            text-align: center;
            position: relative;
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .trend-arrow {
            font-size: 0.8em;
            opacity: 0.8;
        }
        
        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }
        .trend-stable { color: #f39c12; }
        
        .status-change {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        /* 主要内容区域 */
        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card h3 {
            color: #667eea;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        /* 统计卡片增强 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid #667eea;
            position: relative;
            overflow: hidden;
        }
        
        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .stat-trend {
            font-size: 0.8em;
            opacity: 0.7;
        }
        
        /* 交易对列表增强 */
        .symbol-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .symbol-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .symbol-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        
        .symbol-rank {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .symbol-info {
            flex: 1;
            margin-left: 15px;
        }
        
        .symbol-name {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        
        .symbol-volume {
            color: #666;
            font-size: 0.9em;
            margin-top: 2px;
        }
        
        .symbol-stats {
            text-align: right;
        }
        
        .symbol-price {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }
        
        .symbol-change {
            font-size: 0.9em;
            margin-top: 2px;
        }
        
        .change-positive { color: #27ae60; }
        .change-negative { color: #e74c3c; }
        
        /* 强制平仓流增强 */
        .liquidation-list {
            max-height: 450px;
            overflow-y: auto;
        }
        
        .liquidation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            animation: slideInRight 0.5s ease-out;
            transition: all 0.3s ease;
        }
        
        .liquidation-item.buy {
            background: linear-gradient(135deg, #d5f4e6 0%, #c8f7c5 100%);
            border-left: 4px solid #27ae60;
        }
        
        .liquidation-item.sell {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-left: 4px solid #e74c3c;
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .liquidation-symbol {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }
        
        .liquidation-time {
            font-size: 0.8em;
            color: #666;
            margin-top: 3px;
        }
        
        .liquidation-details {
            text-align: right;
        }
        
        .side-badge {
            padding: 4px 12px;
            border-radius: 15px;
            color: white;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 5px;
            display: inline-block;
        }
        
        .side-buy { background: #27ae60; }
        .side-sell { background: #e74c3c; }
        
        .liquidation-amount {
            font-weight: bold;
            color: #333;
        }
        
        /* 底部信息栏增强 */
        .footer-info {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .data-flow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .flow-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        
        .flow-arrow {
            color: #f39c12;
            font-size: 1.2em;
        }
        
        .system-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric-item {
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .metric-label {
            font-size: 0.8em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Redis Stream & Pub/Sub 增强演示</h1>
            <p>展示ClickHouse数据读取 + Redis实时处理 + WebSocket推送 + 增强交互</p>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-group">
                <div class="status-indicator">
                    <div class="status-dot status-online" id="redisStatus"></div>
                    <span>Redis</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot status-online" id="clickhouseStatus"></div>
                    <span>ClickHouse</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot status-online" id="websocketStatus"></div>
                    <span>WebSocket</span>
                </div>
            </div>
            
            <div class="control-group">
                <button class="control-button" onclick="toggleUpdates()">
                    <span id="updateToggle">⏸️ 暂停更新</span>
                </button>
                <button class="control-button" onclick="changeRefreshRate()">
                    <span id="refreshRate">🔄 5秒刷新</span>
                </button>
                <button class="control-button" onclick="toggleTheme()">
                    🌙 深色模式
                </button>
            </div>
        </div>
        
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value">
                    <span id="totalOrders">-</span>
                    <span class="trend-arrow trend-up" id="ordersArrow">↗️</span>
                </div>
                <div>总订单数</div>
                <div class="status-change" id="ordersChange">+5.2% vs 上小时</div>
            </div>
            <div class="status-item">
                <div class="status-value">
                    <span id="avgQuantity">-</span>
                    <span class="trend-arrow trend-stable" id="quantityArrow">➡️</span>
                </div>
                <div>平均数量</div>
                <div class="status-change" id="quantityChange">-1.1% vs 上小时</div>
            </div>
            <div class="status-item">
                <div class="status-value">
                    <span id="uniqueSymbols">-</span>
                    <span class="trend-arrow trend-up" id="symbolsArrow">↗️</span>
                </div>
                <div>交易对数</div>
                <div class="status-change" id="symbolsChange">+3.8% vs 上小时</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="updateTime">-</div>
                <div>更新时间</div>
                <div class="status-change">延迟: <span id="latency">23ms</span></div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-grid">
            <!-- 实时统计卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>📊 实时统计 (最近1小时)</h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="showChart('stats')">📈 图表</button>
                        <button class="action-btn" onclick="exportData('stats')">📤 导出</button>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="sellOrders">-</span>
                            <span class="trend-arrow trend-down">↘️</span>
                        </div>
                        <div class="stat-label">卖单数量</div>
                        <div class="stat-trend">占比: <span id="sellRatio">74%</span></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="buyOrders">-</span>
                            <span class="trend-arrow trend-up">↗️</span>
                        </div>
                        <div class="stat-label">买单数量</div>
                        <div class="stat-trend">占比: <span id="buyRatio">26%</span></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="totalQuantity">-</span>
                            <span class="trend-arrow trend-up">↗️</span>
                        </div>
                        <div class="stat-label">总交易量</div>
                        <div class="stat-trend">24h: +12.5%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">ClickHouse</div>
                        <div class="stat-label">数据源</div>
                        <div class="stat-trend">响应: <span id="dbLatency">45ms</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 热门交易对卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>🏆 热门交易对 (Redis Sorted Set)</h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="showMore('symbols')">📋 更多</button>
                        <button class="action-btn" onclick="refreshSymbols()">🔄 刷新</button>
                    </div>
                </div>
                <div class="symbol-list" id="symbolList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        等待数据加载...
                    </div>
                </div>
            </div>
            
            <!-- 实时强制平仓卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>🔥 实时强制平仓 (Redis Stream)</h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="pauseStream()">
                            <span id="streamToggle">⏸️ 暂停</span>
                        </button>
                        <button class="action-btn" onclick="clearStream()">🗑️ 清空</button>
                    </div>
                </div>
                <div class="liquidation-list" id="liquidationList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        等待实时数据...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部信息栏 -->
        <div class="footer-info">
            <div class="data-flow">
                <div class="flow-item">ClickHouse</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">Redis Stream</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">Pub/Sub</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">WebSocket</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">前端页面</div>
            </div>
            
            <div class="system-metrics">
                <div class="metric-item">
                    <div class="metric-value" id="streamLength">-</div>
                    <div class="metric-label">Stream队列长度</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="pubsubClients">-</div>
                    <div class="metric-label">Pub/Sub订阅者</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="wsConnections">-</div>
                    <div class="metric-label">WebSocket连接</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="totalLatency">-</div>
                    <div class="metric-label">端到端延迟</div>
                </div>
            </div>
            
            <div style="margin-top: 15px; opacity: 0.8;">
                最后更新: <span id="lastUpdate">-</span> | 
                Redis版本: 7.x | 
                处理记录: <span id="processedCount">405万+</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let socket;
        let isUpdating = true;
        let refreshInterval = 5000;
        let streamPaused = false;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initWebSocket();
            console.log('🚀 增强版Redis演示页面加载完成');
        });
        
        // WebSocket初始化
        function initWebSocket() {
            socket = io();
            
            socket.on('connect', () => {
                updateConnectionStatus('websocket', true);
                console.log('🔌 WebSocket连接成功');
            });
            
            socket.on('disconnect', () => {
                updateConnectionStatus('websocket', false);
                console.log('❌ WebSocket连接断开');
            });
            
            socket.on('realtime-data', (data) => {
                if (isUpdating) {
                    console.log('📊 收到实时数据:', data);
                    updateUI(data);
                }
            });
        }
        
        // 更新连接状态
        function updateConnectionStatus(service, isOnline) {
            const statusElement = document.getElementById(service + 'Status');
            if (statusElement) {
                statusElement.className = `status-dot ${isOnline ? 'status-online' : 'status-offline'}`;
            }
        }
        
        // 更新UI (简化版本，完整版本会更复杂)
        function updateUI(data) {
            const { stats, topSymbols, latestLiquidations } = data;
            
            // 更新状态栏
            document.getElementById('totalOrders').textContent = formatNumber(stats.total_orders || 0);
            document.getElementById('avgQuantity').textContent = formatNumber(stats.avg_quantity || 0);
            document.getElementById('uniqueSymbols').textContent = stats.unique_symbols || 0;
            document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
            
            // 更新统计数据
            document.getElementById('sellOrders').textContent = formatNumber(stats.sell_orders || 0);
            document.getElementById('buyOrders').textContent = formatNumber(stats.buy_orders || 0);
            document.getElementById('totalQuantity').textContent = formatNumber(stats.total_quantity || 0);
            
            // 计算比例
            const total = (stats.sell_orders || 0) + (stats.buy_orders || 0);
            if (total > 0) {
                document.getElementById('sellRatio').textContent = Math.round((stats.sell_orders / total) * 100) + '%';
                document.getElementById('buyRatio').textContent = Math.round((stats.buy_orders / total) * 100) + '%';
            }
            
            // 更新交易对列表
            updateSymbolList(topSymbols || []);
            
            // 更新强制平仓列表
            if (!streamPaused) {
                updateLiquidationList(latestLiquidations || []);
            }
            
            // 更新最后更新时间
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
            
            // 模拟系统指标
            document.getElementById('streamLength').textContent = Math.floor(Math.random() * 1000) + 500;
            document.getElementById('pubsubClients').textContent = Math.floor(Math.random() * 10) + 1;
            document.getElementById('wsConnections').textContent = Math.floor(Math.random() * 5) + 1;
            document.getElementById('totalLatency').textContent = Math.floor(Math.random() * 50) + 20 + 'ms';
            document.getElementById('latency').textContent = Math.floor(Math.random() * 30) + 15 + 'ms';
            document.getElementById('dbLatency').textContent = Math.floor(Math.random() * 80) + 30 + 'ms';
        }
        
        // 更新交易对列表 (增强版)
        function updateSymbolList(symbols) {
            const container = document.getElementById('symbolList');
            
            if (symbols.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无数据</div>';
                return;
            }
            
            container.innerHTML = symbols.map((symbol, index) => `
                <div class="symbol-item" onclick="showSymbolDetails('${symbol.symbol}')">
                    <div class="symbol-rank">${index + 1}</div>
                    <div class="symbol-info">
                        <div class="symbol-name">${symbol.symbol}</div>
                        <div class="symbol-volume">交易量: ${formatNumber(symbol.total_quantity)}</div>
                    </div>
                    <div class="symbol-stats">
                        <div class="symbol-price">$${formatNumber(symbol.avg_price)}</div>
                        <div class="symbol-change change-positive">+${Math.random() * 5 + 1}%</div>
                    </div>
                </div>
            `).join('');
        }
        
        // 更新强制平仓列表 (增强版)
        function updateLiquidationList(liquidations) {
            const container = document.getElementById('liquidationList');
            
            if (liquidations.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无数据</div>';
                return;
            }
            
            container.innerHTML = liquidations.map(liquidation => `
                <div class="liquidation-item ${liquidation.side.toLowerCase()}">
                    <div>
                        <div class="liquidation-symbol">${liquidation.symbol}</div>
                        <div class="liquidation-time">
                            ${getRelativeTime(liquidation.timestamp)}
                        </div>
                    </div>
                    <div class="liquidation-details">
                        <div>
                            <span class="side-badge side-${liquidation.side.toLowerCase()}">
                                ${liquidation.side}
                            </span>
                        </div>
                        <div class="liquidation-amount">
                            <div>数量: ${formatNumber(liquidation.quantity)}</div>
                            <div>价格: $${formatNumber(liquidation.price)}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // 控制函数
        function toggleUpdates() {
            isUpdating = !isUpdating;
            document.getElementById('updateToggle').textContent = isUpdating ? '⏸️ 暂停更新' : '▶️ 继续更新';
        }
        
        function changeRefreshRate() {
            const rates = [1000, 5000, 10000];
            const labels = ['1秒', '5秒', '10秒'];
            const currentIndex = rates.indexOf(refreshInterval);
            const nextIndex = (currentIndex + 1) % rates.length;
            refreshInterval = rates[nextIndex];
            document.getElementById('refreshRate').textContent = `🔄 ${labels[nextIndex]}刷新`;
        }
        
        function pauseStream() {
            streamPaused = !streamPaused;
            document.getElementById('streamToggle').textContent = streamPaused ? '▶️ 继续' : '⏸️ 暂停';
        }
        
        function toggleTheme() {
            // 主题切换逻辑
            console.log('切换主题');
        }
        
        // 工具函数
        function formatNumber(num) {
            if (num === null || num === undefined) return '-';
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            } else {
                return parseFloat(num).toFixed(2);
            }
        }
        
        function getRelativeTime(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diff = Math.floor((now - time) / 1000);
            
            if (diff < 60) return `${diff}秒前`;
            if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
            return time.toLocaleTimeString();
        }
        
        // 占位函数
        function showChart(type) { console.log('显示图表:', type); }
        function exportData(type) { console.log('导出数据:', type); }
        function showMore(type) { console.log('显示更多:', type); }
        function refreshSymbols() { console.log('刷新交易对'); }
        function clearStream() { console.log('清空流'); }
        function showSymbolDetails(symbol) { console.log('显示详情:', symbol); }
    </script>
</body>
</html>
