/**
 * 简化的Redis Stream和Pub/Sub演示服务器
 * 展示ClickHouse数据读取和Redis实时处理
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const Redis = require('ioredis');
const axios = require('axios');
const cors = require('cors');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// Redis连接
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

const redisPub = new Redis({
  host: 'localhost',
  port: 6379
});

// ClickHouse配置
const CLICKHOUSE_CONFIG = {
  host: '***********',
  port: 8123,
  user: 'myuser',
  password: 'mypassword',
  database: 'mydatabase',
  table: 'force_orders'
};

// ClickHouse查询函数
async function queryClickHouse(sql) {
  try {
    const response = await axios.post(`http://${CLICKHOUSE_CONFIG.host}:${CLICKHOUSE_CONFIG.port}`, sql, {
      params: {
        user: CLICKHOUSE_CONFIG.user,
        password: CLICKHOUSE_CONFIG.password,
        database: CLICKHOUSE_CONFIG.database,
        default_format: 'JSON'
      },
      timeout: 30000
    });
    return response.data;
  } catch (error) {
    console.error('ClickHouse查询错误:', error.message);
    throw error;
  }
}

// 获取实时统计数据
async function getRealtimeStats() {
  try {
    const sql = `
      SELECT
        count() as total_orders,
        countIf(side = 'SELL') as sell_orders,
        countIf(side = 'BUY') as buy_orders,
        round(avg(quantity), 2) as avg_quantity,
        round(sum(quantity), 2) as total_quantity,
        uniq(symbol) as unique_symbols
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
    `;

    const result = await queryClickHouse(sql);
    return result.data[0] || {};
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return {};
  }
}

// 获取热门交易对
async function getTopSymbols() {
  try {
    const sql = `
      SELECT
        symbol,
        count() as order_count,
        round(sum(quantity), 2) as total_quantity,
        round(avg(price), 2) as avg_price
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
      GROUP BY symbol
      ORDER BY order_count DESC
      LIMIT 10
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取热门交易对失败:', error);
    return [];
  }
}

// 获取最新强制平仓记录
async function getLatestLiquidations() {
  try {
    const sql = `
      SELECT
        symbol,
        side,
        quantity,
        price,
        event_time as timestamp
      FROM ${CLICKHOUSE_CONFIG.table}
      ORDER BY event_time DESC
      LIMIT 20
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取最新记录失败:', error);
    return [];
  }
}

// Redis Stream处理
async function processDataStream() {
  try {
    // 创建Stream（如果不存在）
    await redis.xadd('liquidation:stream', '*', 'type', 'init', 'timestamp', Date.now());
    
    // 创建消费者组
    try {
      await redis.xgroup('CREATE', 'liquidation:stream', 'demo-group', '$', 'MKSTREAM');
    } catch (error) {
      if (!error.message.includes('BUSYGROUP')) {
        console.error('创建消费者组失败:', error);
      }
    }
    
    console.log('✅ Redis Stream初始化完成');
  } catch (error) {
    console.error('❌ Redis Stream初始化失败:', error);
  }
}

// 定期推送数据到Redis Stream和Pub/Sub
async function startDataPushing() {
  setInterval(async () => {
    try {
      // 获取实时数据
      const stats = await getRealtimeStats();
      const topSymbols = await getTopSymbols();
      const latestLiquidations = await getLatestLiquidations();
      
      const data = {
        timestamp: Date.now(),
        stats,
        topSymbols,
        latestLiquidations: latestLiquidations.slice(0, 5) // 只取前5条
      };
      
      // 推送到Redis Stream
      await redis.xadd('liquidation:stream', '*', 
        'data', JSON.stringify(data),
        'timestamp', Date.now()
      );
      
      // 推送到Redis Pub/Sub
      await redisPub.publish('liquidation:updates', JSON.stringify(data));
      
      // 推送到WebSocket客户端
      io.emit('realtime-data', data);
      
      console.log(`📊 数据推送完成 - ${new Date().toLocaleTimeString()}`);
      
    } catch (error) {
      console.error('数据推送失败:', error);
    }
  }, 5000); // 每5秒推送一次
}

// API路由
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      redis: 'connected',
      clickhouse: 'connected',
      websocket: 'running'
    }
  });
});

app.get('/api/stats', async (req, res) => {
  try {
    const stats = await getRealtimeStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/symbols', async (req, res) => {
  try {
    const symbols = await getTopSymbols();
    res.json(symbols);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/liquidations', async (req, res) => {
  try {
    const liquidations = await getLatestLiquidations();
    res.json(liquidations);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log('🔌 客户端连接:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('❌ 客户端断开:', socket.id);
  });
  
  // 发送初始数据
  socket.emit('connected', { message: 'WebSocket连接成功' });
});

// 启动服务器
const PORT = process.env.PORT || 3002;

server.listen(PORT, async () => {
  console.log(`🚀 Redis演示服务器启动在端口 ${PORT}`);
  console.log(`📱 前端页面: http://localhost:${PORT}`);
  console.log(`🔗 API健康检查: http://localhost:${PORT}/api/health`);
  
  // 初始化Redis Stream
  await processDataStream();
  
  // 开始数据推送
  startDataPushing();
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  redis.disconnect();
  redisPub.disconnect();
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
