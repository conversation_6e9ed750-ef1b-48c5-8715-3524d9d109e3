/**
 * 数据上下文
 * 管理应用的数据状态和WebSocket连接
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import io from 'socket.io-client';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState({
    websocket: false,
    api: false,
    redis: false
  });
  const [realtimeData, setRealtimeData] = useState({
    liquidations: [],
    statistics: {},
    rankings: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // WebSocket连接
  useEffect(() => {
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:3001';
    console.log('🔌 连接WebSocket:', wsUrl);
    
    const newSocket = io(wsUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    newSocket.on('connect', () => {
      console.log('✅ WebSocket连接成功');
      setConnectionStatus(prev => ({ ...prev, websocket: true }));
      setError(null);
      setLoading(false);
      // 连接成功后检查其他服务状态
      checkServiceStatus();
    });

    newSocket.on('disconnect', () => {
      console.log('❌ WebSocket连接断开');
      setConnectionStatus(prev => ({ ...prev, websocket: false }));
    });

    newSocket.on('connect_error', (error) => {
      console.error('❌ WebSocket连接错误:', error);
      setConnectionStatus(prev => ({ ...prev, websocket: false }));
      setError(error.message);
      setLoading(false);
    });

    // 监听实时数据
    newSocket.on('liquidation:new', (data) => {
      console.log('📊 收到新的强制平仓数据:', data);
      setRealtimeData(prev => ({
        ...prev,
        liquidations: [data, ...prev.liquidations.slice(0, 99)] // 保持最新100条
      }));
    });

    newSocket.on('statistics:update', (data) => {
      console.log('📈 收到统计数据更新:', data);
      setRealtimeData(prev => ({
        ...prev,
        statistics: data
      }));
    });

    newSocket.on('rankings:update', (data) => {
      console.log('🏆 收到排行榜更新:', data);
      setRealtimeData(prev => ({
        ...prev,
        rankings: data
      }));
    });

    setSocket(newSocket);

    return () => {
      console.log('🔌 断开WebSocket连接');
      newSocket.close();
    };
  }, []);

  // 检查服务状态
  const checkServiceStatus = async () => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/v1/health`);
      if (response.ok) {
        const healthData = await response.json();
        setConnectionStatus(prev => ({
          ...prev,
          api: true,
          redis: healthData.services?.redis?.status === 'healthy'
        }));
      } else {
        setConnectionStatus(prev => ({ ...prev, api: false, redis: false }));
      }
    } catch (error) {
      console.error('❌ 检查服务状态失败:', error);
      setConnectionStatus(prev => ({ ...prev, api: false, redis: false }));
    }
  };

  // 定期检查服务状态
  useEffect(() => {
    checkServiceStatus();
    const interval = setInterval(checkServiceStatus, 10000); // 每10秒检查一次
    return () => clearInterval(interval);
  }, []);

  // API请求函数
  const fetchData = async (endpoint) => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/v1${endpoint}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`❌ 获取数据失败 (${endpoint}):`, error);
      setError(error.message);
      throw error;
    }
  };

  // 获取实时统计
  const getRealtimeStats = () => fetchData('/statistics/realtime');
  
  // 获取排行榜
  const getRankings = (type = 'volume') => fetchData(`/rankings/${type}`);
  
  // 获取健康状态
  const getHealthStatus = () => fetchData('/health');

  const value = {
    socket,
    connectionStatus,
    realtimeData,
    loading,
    error,
    // API方法
    fetchData,
    getRealtimeStats,
    getRankings,
    getHealthStatus,
    checkServiceStatus,
    // 状态更新方法
    setError,
    setLoading
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
